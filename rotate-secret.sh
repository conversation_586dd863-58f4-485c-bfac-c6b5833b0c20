#!/bin/bash

API=$1

curl \
  -o ${API}.json \
  "https://appgateway.webuycars.co.za/identity-server-api/v1/Clients/${API}/secrets" \
  -H 'accept: */*' \
  -H 'Authorization: Bearer *********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************' \
  -H 'Content-Type: application/json-patch+json' \
  -d '{
  "description": "AutoRotation 20260601",
  "expiryDate": "2026-06-01T00:00:00.000Z",
  "createdDate": "2025-07-07T00:00:00.000Z",
  "type": "SharedSecret"
}'
> /dev/null 2>&1

SECRET=$(cat ${API}.json | jq -r '.value')

echo $API $SECRET

secrets()
{
	awk '{print "\"" $1 "\": \"" $2 "\""}' secrets.txt | paste -sd, - | awk '{print "{ " $0 " }"}' > secrets.json
}


combine()
{
jq --slurpfile secrets secrets.json '
  .IdentitySettings.ApiSettings |=
    with_entries(.value.ClientSecret = ($secrets[0][.value.ClientId] // .value.ClientSecret))
' config.json > updated_config.json
}
