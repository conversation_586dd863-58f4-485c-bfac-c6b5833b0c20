#!/bin/bash

usage()
{
    echo "usage: `basename $0`: [Environment]"
    echo "  Environment: Development (default), Staging, Staging.NAM, Production, NAM"
    echo ""
    echo "This script extracts all API names from Azure App Configuration for the specified environment."
    echo "It looks for keys matching the pattern: IdentitySettings:ApiSettings:*:ClientId"
    exit 1
}

# Check for help flag
if [[ "$1" == "-h" || "$1" == "--help" ]]; then
    usage
fi

ENV=${1:-Development}

# Set environment-specific configuration
case $ENV in
  Development)
    APPCONFIG_NAME="WBC-Appconfig-Development"
    SUBSCRIPTION="WeBuyCars DEVTEST"
    ;;
  Staging)
    APPCONFIG_NAME="WBC-Appconfig-Staging"
    SUBSCRIPTION="WeBuyCars DEVTEST"
    ;;
  Staging.NAM)
    APPCONFIG_NAME="WBC-Appconfig-Staging"
    SUBSCRIPTION="WeBuyCars DEVTEST"
    ;;
  Production)
    APPCONFIG_NAME="WBC-Appconfig-Prod"
    SUBSCRIPTION="WeBuyCars PROD"
    ;;
  NAM)
    APPCONFIG_NAME="WBC-Appconfig-Prod"
    SUBSCRIPTION="WeBuyCars PROD"
    ;;
  *)
    echo "Error: Unknown environment: $ENV"
    echo "Valid environments: Development, Staging, Staging.NAM, Production, NAM"
    exit 1
    ;;
esac

get_api_names()
{
    local response
    response=$(az appconfig kv list \
        --subscription "$SUBSCRIPTION" \
        --name "$APPCONFIG_NAME" \
        --label "$ENV" \
        --query "[].key" \
        --output json \
        --all 2>/dev/null)

    if [[ $? -ne 0 ]]; then
        echo "Error: Failed to retrieve configuration from Azure App Configuration" >&2
        return 1
    fi

    if [[ -z "$response" || "$response" == "[]" ]]; then
        echo "Warning: No configurations found for environment: ${ENV}" >&2
        return 0
    fi

    # Filter keys that match the pattern IdentitySettings:ApiSettings:*:ClientId and extract API names
    local api_names
    api_names=$(echo "$response" | jq -r '.[] | select(test("^IdentitySettings:ApiSettings:.+:ClientId$")) | split(":") | .[2]' | sort -u)

    if [[ $? -ne 0 ]]; then
        echo "Error: Failed to parse API names from response" >&2
        echo "Response: ${response}" >&2
        return 1
    fi

    if [[ -z "$api_names" ]]; then
        echo "Warning: No API configurations matching the pattern found for environment: ${ENV}" >&2
        echo "Looking for keys matching: IdentitySettings:ApiSettings:*:ClientId" >&2
        return 0
    fi

    # Output the API names
    echo "$api_names"
}

# Validate Azure CLI is available
if ! command -v az &> /dev/null; then
    echo "Error: Azure CLI (az) is not installed or not in PATH"
    exit 1
fi

# Validate jq is available
if ! command -v jq &> /dev/null; then
    echo "Error: jq is not installed or not in PATH"
    exit 1
fi

# Check if user is logged into Azure CLI
if ! az account show &> /dev/null; then
    echo "Error: Not logged into Azure CLI. Please run 'az login' first."
    exit 1
fi

# Main execution
API_NAMES=$(get_api_names)
exit_code=$?

if [[ $exit_code -ne 0 ]]; then
    echo "Error: Failed to retrieve API names"
    exit 1
fi

if [[ -n "$API_NAMES" ]]; then
    echo "$API_NAMES"
else
    echo "No API names found for environment: ${ENV}" >&2
fi
