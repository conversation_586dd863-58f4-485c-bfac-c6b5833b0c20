#!/bin/bash

usage()
{
    echo "usage: `basename $0`: [Environment]"
    echo "  Environment: Development (default), Staging, Staging.NAM, Production, NAM"
    exit 1
}

[ $# -lt 1 ] && usage

ENV=${1:-Development}

get_token()
{
    CLIENT_ID=$(az appconfig kv list \
     --subscription "$SUBSCRIPTION" \
     --name "$APPCONFIG_NAME" \
     --label "$ENV" \
     --key "IdentitySettings:ApiSettings:identity-server-api:ClientId" \
     --query "[0].value" \
     --output tsv)

    CLIENT_SECRET=$(az appconfig kv list \
     --subscription "$SUBSCRIPTION" \
     --name "$APPCONFIG_NAME" \
     --label "$ENV" \
     --key "IdentitySettings:ApiSettings:identity-server-api:ClientSecret" \
     --query "[0].value" \
     --output tsv)

    curl -s -X POST "${IDENTITY_SERVER}/connect/token" \
    -H "Content-Type: application/x-www-form-urlencoded" \
    -d "grant_type=client_credentials" \
    -d "client_id=${CLIENT_ID}" \
    -d "client_secret=${CLIENT_SECRET}" \
    -d "scope=IdentityServerApi" | jq -r '.access_token'
}

get_api_names()
{
    local response
    response=$(az appconfig kv list \
        --subscription "$SUBSCRIPTION" \
        --name "$APPCONFIG_NAME" \
        --label "$ENV" \
        --query "[].key" \
        --output json \
        --all 2>/dev/null)

    if [[ $? -ne 0 ]]; then
        echo "Error: Failed to retrieve configuration from Azure App Configuration" >&2
        return 1
    fi

    if [[ -z "$response" || "$response" == "[]" ]]; then
        echo "Warning: No configurations found for environment: ${ENV}" >&2
        return 0
    fi

    local api_names
    api_names=$(echo "$response" | jq -r '.[] | select(test("^IdentitySettings:ApiSettings:.+:ClientId$")) | split(":") | .[2]' | sort -u)

    if [[ $? -ne 0 ]]; then
        echo "Error: Failed to parse API names from response" >&2
        echo "Response: ${response}" >&2
        return 1
    fi

    if [[ -z "$api_names" ]]; then
        echo "Warning: No API configurations matching the pattern found for environment: ${ENV}" >&2
        echo "Looking for keys matching: IdentitySettings:ApiSettings:*:ClientId" >&2
        return 0
    fi

    echo "$api_names"
}

get_client_id()
{
    # Get the ClientId for the specified API_NAME from Azure App Configuration
    CLIENT_ID=$(az appconfig kv list \
      --subscription "$SUBSCRIPTION" \
      --name "$APPCONFIG_NAME" \
      --label "$ENV" \
      --key "IdentitySettings:ApiSettings:${API_NAME}:ClientId" \
      --query "[0].value" \
      --output tsv)

    if [[ -z "$CLIENT_ID" ]]; then
        return 1
    fi

    echo "$CLIENT_ID"
}

get_secret()
{
    local created_date=$(date -u +"%Y-%m-%dT%H:%M:%S.000Z")
    local expiry_date=$(date -u -v+6m +"%Y-%m-%dT%H:%M:%S.000Z")
    local description=$(date -u -v+6m +"%Y-%m-%d")
    local api_url="${IDENTITY_SERVER}/v1/Clients/${API_CLIENT_ID}/secrets"
    local response=$(curl -s -w "\n%{http_code}" "${api_url}" \
        -H 'accept: */*' \
        -H "Authorization: Bearer ${TOKEN}" \
        -H 'Content-Type: application/json' \
        -d "{
        \"description\": \"AutoRotation ${description}\",
        \"expiryDate\": \"${expiry_date}\",
        \"createdDate\": \"${created_date}\",
        \"type\": \"SharedSecret\"
        }")

    if [[ $? -ne 0 ]]; then
        echo "Error: curl command failed" >&2
        return 1
    fi

    local response_body=$(echo "$response" | head -1)
    local http_code=$(echo "$response" | tail -1)

    if [[ "$http_code" -lt 200 || "$http_code" -ge 300 ]]; then
        echo "Error: API call failed with HTTP status ${http_code}" >&2
        echo "Response: ${response_body}" >&2
        return 1
    fi

    local secret_value=$(echo "$response_body" | jq -r '.value')
    if [[ $? -ne 0 ]]; then
        echo "Error: Failed to parse JSON response" >&2
        echo "Response: ${response_body}" >&2
        return 1
    fi

    if [[ -z "$secret_value" || "$secret_value" == "null" ]]; then
        echo "Error: No secret value found in response" >&2
        echo "Response: ${response_body}" >&2
        return 1
    fi

    echo "$secret_value"
}

update_secret()
{
    local key_name="IdentitySettings:ApiSettings:${API_NAME}:ClientSecret"

    if [[ "$ENV" == "Development" || "$ENV" == "Staging" ]]; then
      echo "Updating Development environment..."
      az appconfig kv set \
        --subscription "$SUBSCRIPTION" \
        --name "WBC-Appconfig-Development" \
        --label "Development" \
        --key "$key_name" \
        --value "$SECRET" \
        --yes

      echo "Updating Staging environment..."
      az appconfig kv set \
        --subscription "$SUBSCRIPTION" \
        --name "WBC-Appconfig-Staging" \
        --label "Staging" \
        --key "$key_name" \
        --value "$SECRET" \
        --yes
    else
      echo "Updating ${ENV} environment..."
      az appconfig kv set \
        --subscription "$SUBSCRIPTION" \
        --name "$APPCONFIG_NAME" \
        --label "$ENV" \
        --key "$key_name" \
        --value "$SECRET" \
        --yes
    fi
}

case $ENV in
  Development)
    APPCONFIG_NAME="WBC-Appconfig-Development"
    SUBSCRIPTION="WeBuyCars DEVTEST"
    IDENTITY_SERVER="https://appgateway.development.webuycars.co.za/identity-server-api"
    ;;
  Staging)
    APPCONFIG_NAME="WBC-Appconfig-Staging"
    SUBSCRIPTION="WeBuyCars DEVTEST"
    IDENTITY_SERVER="https://appgateway.staging.webuycars.co.za/identity-server-api"
    ;;
  Staging.NAM)
    APPCONFIG_NAME="WBC-Appconfig-Staging"
    SUBSCRIPTION="WeBuyCars DEVTEST"
    IDENTITY_SERVER="https://appgateway.staging.webuycars.na/identity-server-api"
    ;;
  Production)
    APPCONFIG_NAME="WBC-Appconfig-Prod"
    IDENTITY_SERVER="https://identity.webuycars.co.za"
    SUBSCRIPTION="WeBuyCars PROD"
    ;;
  NAM)
    APPCONFIG_NAME="WBC-Appconfig-Prod"
    IDENTITY_SERVER="https://appgateway.webuycars.na/identity-server-api"
    SUBSCRIPTION="WeBuyCars PROD"
    ;;
  *)
    echo "Unknown environment: $ENV"
    exit 1
    ;;
esac

TOKEN=$(get_token)
if [[ -z "$TOKEN" ]]; then
    echo "Error: Failed to get authentication token"
    exit 1
fi

API_NAMES=$(get_api_names)

for API_NAME in $API_NAMES; do 
    API_CLIENT_ID=$(get_client_id)
    if [[ $? -ne 0 || -z "$API_CLIENT_ID" ]]; then
        echo "Error: Failed to get ClientId for ${API_NAME}"
        continue
    fi

    if [[ "$API_CLIENT_ID" == "notset" ]]; then
        echo "Skipping ${API_NAME} as ClientId is not set"
        continue
    fi

    if [[ "$API_CLIENT_ID" == "Identity.Server.B2B.Client" ]]; then
        echo "Skipping ${API_NAME}"
        continue
    fi

    SECRET=$(get_secret)
    if [[ $? -ne 0 || -z "$SECRET" ]]; then
        echo "Error: Failed to generate new secret for ${API_NAME}"
        continue;
    fi

    update_secret

    echo "Rotated secret: ${ENV} ${API_NAME} ${API_CLIENT_ID} ${SECRET}"
done    
